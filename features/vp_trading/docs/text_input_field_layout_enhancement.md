# TextInputField Layout Enhancement

## Overview

The `TextInputField` component has been enhanced to support flexible layout options while maintaining full backward compatibility. This enhancement allows the price and volume input fields to be arranged either horizontally (existing behavior) or vertically (new option).

## Key Features

### 1. Layout Flexibility
- **Horizontal Layout**: Price and volume fields side by side (default/existing behavior)
- **Vertical Layout**: Price and volume fields stacked vertically (new option)

### 2. Backward Compatibility
- All existing code continues to work without any changes
- Default behavior remains exactly the same
- No breaking changes to the API

### 3. Customization Options
- **Custom Spacing**: Control spacing between fields
- **Custom Padding**: Override default padding
- **Responsive Design**: Layout can change based on screen size or other conditions

## API Changes

### New Parameters

```dart
class TextInputField extends StatefulWidget {
  final TextEditingController priceController;
  final TextEditingController volumeController;
  final TextInputFieldLayout layout;           // NEW: Layout direction
  final EdgeInsetsGeometry? padding;          // NEW: Custom padding
  final double? spacing;                      // NEW: Custom spacing

  const TextInputField({
    super.key,
    required this.priceController,
    required this.volumeController,
    this.layout = TextInputFieldLayout.horizontal,  // Default: horizontal
    this.padding,                                   // Optional
    this.spacing,                                   // Optional
  });
}
```

### New Enum

```dart
enum TextInputFieldLayout {
  /// Horizontal layout (row) - default behavior
  horizontal,
  /// Vertical layout (column)
  vertical,
}
```

## Usage Examples

### 1. Default Usage (Backward Compatible)
```dart
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
  // No layout parameter - defaults to horizontal
)
```

### 2. Explicit Horizontal Layout
```dart
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
  layout: TextInputFieldLayout.horizontal,
)
```

### 3. Vertical Layout
```dart
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
  layout: TextInputFieldLayout.vertical,
)
```

### 4. Custom Spacing and Padding
```dart
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
  layout: TextInputFieldLayout.vertical,
  spacing: 16.0,                              // Custom spacing between fields
  padding: EdgeInsets.symmetric(horizontal: 24), // Custom padding
)
```

### 5. Responsive Layout
```dart
LayoutBuilder(
  builder: (context, constraints) {
    final isWideScreen = constraints.maxWidth > 400;
    return TextInputField(
      priceController: _priceController,
      volumeController: _volumeController,
      layout: isWideScreen 
          ? TextInputFieldLayout.horizontal 
          : TextInputFieldLayout.vertical,
      spacing: isWideScreen ? 8.0 : 16.0,
    );
  },
)
```

## Implementation Details

### Architecture
- **Enum-based Layout Control**: Clean, type-safe layout selection
- **Helper Methods**: Separated layout logic into dedicated methods
- **Preserved Functionality**: All existing validation, focus handling, and BLoC integration maintained

### Code Structure
```dart
Widget _buildInputFields(BuildContext context, ValidateOrderState state) {
  // Builds appropriate layout based on widget.layout
}

Widget _buildPriceField(BuildContext context, ValidateOrderState state) {
  // Builds price input field (reusable)
}

Widget _buildVolumeField(BuildContext context, ValidateOrderState state) {
  // Builds volume input field (reusable)
}
```

## Integration with ReusablePriceVolumeWidget

The `ReusablePriceVolumeWidget` has also been updated to support the new layout options:

```dart
ReusablePriceVolumeWidget(
  priceController: _priceController,
  volumeController: _volumeController,
  layout: TextInputFieldLayout.vertical,      // NEW: Layout support
  spacing: 12.0,                             // NEW: Custom spacing
  showOrderSuggest: true,
  showValidation: true,
)
```

## Benefits

### 1. Enhanced UI Flexibility
- Support for different screen sizes and orientations
- Better adaptation to various design requirements
- Improved user experience on different devices

### 2. Maintainable Code
- Single component handles both layouts
- Reduced code duplication
- Consistent behavior across layouts

### 3. Future-Proof Design
- Easy to extend with additional layout options
- Clean separation of concerns
- Type-safe layout selection

## Testing

Comprehensive tests have been added to verify:
- Layout rendering correctness
- Backward compatibility
- Custom spacing and padding
- Responsive behavior
- Enum functionality

## Migration Guide

### For Existing Code
No changes required! All existing usage continues to work exactly as before.

### For New Features
Use the new layout options to create more flexible UIs:

```dart
// Old approach (still works)
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
)

// New approach with layout flexibility
TextInputField(
  priceController: _priceController,
  volumeController: _volumeController,
  layout: TextInputFieldLayout.vertical,  // Choose layout
  spacing: 16.0,                         // Customize spacing
)
```

## Best Practices

1. **Use Default Layout**: For most cases, the default horizontal layout is appropriate
2. **Vertical for Narrow Screens**: Consider vertical layout for mobile or narrow containers
3. **Responsive Design**: Use `LayoutBuilder` for adaptive layouts
4. **Consistent Spacing**: Use consistent spacing values across your app
5. **Test Both Layouts**: Ensure your validation and interaction logic works in both layouts

## Files Modified

- `lib/screen/place_order/widgets/input_view/text_input_field.dart` - Main enhancement
- `lib/screen/place_order/widgets/input_view/price_volume_widget.dart` - Integration support
- `test/screen/place_order/widgets/input_view/text_input_field_layout_test.dart` - Comprehensive tests
- `example/text_input_field_layout_example.dart` - Usage examples
