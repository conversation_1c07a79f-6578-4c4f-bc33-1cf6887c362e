import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class EditCommandBottomSheet extends StatefulWidget {
  final String stockCode;
  final double currentPrice;
  final int currentVolume;

  final Function(double newPrice, int newVolume) onConfirm;
  final StockInfoModel? stockInfo;
  final OrderAction orderAction;
  final OrderType orderType;

  const EditCommandBottomSheet({
    super.key,
    required this.stockCode,
    required this.currentPrice,
    required this.currentVolume,

    required this.onConfirm,
    this.stockInfo,
    this.orderAction = OrderAction.buy,
    this.orderType = OrderType.lo,
  });

  @override
  State<EditCommandBottomSheet> createState() => _EditCommandBottomSheetState();
}

class _EditCommandBottomSheetState extends State<EditCommandBottomSheet> {
  late TextEditingController _priceController;
  late TextEditingController _volumeController;
  late ValidateOrderCubit _validateOrderCubit;

  final _priceFocusNode = FocusNode();
  final _volumeFocusNode = FocusNode();

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      _validateOrderCubit.focusField(FocusKeyboard.price);
    } else if (_volumeFocusNode.hasFocus) {
      _validateOrderCubit.focusField(FocusKeyboard.volume);
    } else {
      _validateOrderCubit.focusField(FocusKeyboard.none);
    }
  }

  @override
  void initState() {
    super.initState();

    // Initialize ValidateOrderCubit
    _validateOrderCubit = ValidateOrderCubit();

    // Initialize controllers with formatted values
    _priceController = TextEditingController(
      text: widget.currentPrice.getPriceFormatted(convertToThousand: true),
    );
    _volumeController = TextEditingController(
      text: widget.currentVolume.volumeString,
    );

    // Add focus listeners
    _priceFocusNode.addListener(_focusListener);
    _volumeFocusNode.addListener(_focusListener);

    // Update ValidateOrderCubit with initial parameters
    _validateOrderCubit.updateParam(
      stockInfo: widget.stockInfo,
      action: widget.orderAction,
      orderType: widget.orderType,
    );

    // Set initial values
    if (_priceController.text.isNotEmpty) {
      _validateOrderCubit.onChangePrice(_priceController.text);
    }
    if (_volumeController.text.isNotEmpty) {
      _validateOrderCubit.onChangeVolumne(_volumeController.text);
    }
  }

  @override
  void dispose() {
    _priceController.dispose();
    _volumeController.dispose();
    _priceFocusNode.dispose();
    _volumeFocusNode.dispose();
    _validateOrderCubit.close();
    super.dispose();
  }

  void _adjustPrice(bool increase) {
    _validateOrderCubit.priceTap(
      text: _priceController.text,
      increase: increase,
    );
  }

  void _adjustVolume(bool increase) {
    _validateOrderCubit.volumneTap(
      text: _volumeController.text,
      increase: increase,
    );
  }

  void _onPriceChanged(String value) {
    _validateOrderCubit.onChangePrice(value);
  }

  void _onVolumeChanged(String value) {
    _validateOrderCubit.onChangeVolumne(value);
  }

  void _handleConfirm() {
    final state = _validateOrderCubit.state;
    if (state.isValid) {
      final newPrice = _priceController.text.price ?? 0.0;
      final newVolume = _volumeController.text.volume.toInt();
      widget.onConfirm(newPrice, newVolume);
      Navigator.pop(context);
    }
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required VoidCallback onDecrease,
    required VoidCallback onIncrease,
    String? errorText,
    FocusNode? focusNode,
    Function(String)? onChange,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyle.body14?.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        VPTextField(
          controller: controller,
          textAlign: TextAlign.center,
          keyboardType: TextInputType.number,
          style: context.textStyle.body14?.copyWith(color: Colors.white),
          inputType: InputType.rest,
          focusNode: focusNode,
          inputFormatters: inputFormatters,
          onChanged: onChange,
          prefixIcon:
              (color) => IconButton(
                icon: Icon(Icons.remove, color: vpColor.iconPrimary, size: 24),
                onPressed: onDecrease,
              ),

          suffixIcon:
              (color) => IconButton(
                icon: Icon(Icons.add, color: vpColor.iconPrimary, size: 24),
                onPressed: onIncrease,
              ),
          caption:
              errorText != null
                  ? (color) => Text(
                    errorText,
                    style: context.textStyle.captionRegular?.copyWith(
                      color: context.colors.textAccentRed,
                    ),
                  )
                  : null,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          bloc: _validateOrderCubit,
          listenWhen:
              (previous, current) =>
                  previous.currentPrice != current.currentPrice,
          listener: (context, state) {
            if (state.currentPrice != null) {
              _priceController.text = state.currentPrice!;
              _priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: _priceController.text.length),
              );
            }
          },
        ),
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          bloc: _validateOrderCubit,
          listenWhen:
              (previous, current) =>
                  previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            if (state.currentVolume != null) {
              _volumeController.text = state.currentVolume!;
              _volumeController.selection = TextSelection.fromPosition(
                TextPosition(offset: _volumeController.text.length),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
        bloc: _validateOrderCubit,
        builder: (context, state) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),

              Text(
                widget.orderAction == OrderAction.buy
                    ? "Lệnh bán"
                    : VPTradingLocalize.current.trading_buy_order_title,
                style: context.textStyle.subtitle16?.copyWith(
                  color: context.colors.textPrimary,
                ),
              ),
              const SizedBox(height: 16),

              VPTextField(
                hintText: widget.stockCode,
                textAlign: TextAlign.start,
                inputType: InputType.disabled,
              ),

              const SizedBox(height: 16),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    VPTradingLocalize.current.trading_buying_power,
                    style: context.textStyle.body14?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
                    builder: (context, state) {
                      if (state.availableTrade != null) {
                        return Text(
                          MoneyUtils.formatMoney(
                            (state.availableTrade!.pp0 ?? 0).toDouble(),
                          ),
                          style: context.textStyle.subtitle16?.copyWith(
                            color: Colors.white,
                          ),
                        );
                      }
                      return const Text('');
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),

              _buildInputField(
                controller: _priceController,
                label: VPTradingLocalize.current.trading_price,
                onDecrease: () => _adjustPrice(false),
                onIncrease: () => _adjustPrice(true),
                errorText:
                    state.errorPrice.isError ? state.errorPrice.message : null,
                focusNode: _priceFocusNode,
                onChange: _onPriceChanged,
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...priceInputFormatter,
                ],
              ),
              const SizedBox(height: 16),

              _buildInputField(
                controller: _volumeController,
                label: VPTradingLocalize.current.trading_volume,
                onDecrease: () => _adjustVolume(false),
                onIncrease: () => _adjustVolume(true),
                errorText:
                    state.errorVolume.isError
                        ? state.errorVolume.message(
                          _validateOrderCubit.maxVolume().toString(),
                        )
                        : null,
                focusNode: _volumeFocusNode,
                onChange: _onVolumeChanged,
                inputFormatters: [
                  removeZeroStartInputFormatter,
                  ...volumeInputFormatter,
                ],
              ),
              const SizedBox(height: 8),

              BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
                builder: (context, state) {
                  if (state.availableTrade == null) {
                    return const Text('');
                  }
                  final maxVolume =
                      widget.orderAction == OrderAction.buy
                          ? state.availableTrade!.maxBuyQty
                          : state.availableTrade!.maxSellQty;
                  return Text(
                    "${VPTradingLocalize.current.trading_max_volume}: ${MoneyUtils.formatMoney((maxVolume ?? 0).toDouble(), suffix: '')}",
                    style: context.textStyle.captionRegular?.copyWith(
                      color: Colors.grey[600],
                    ),
                  );
                },
              ),
              const SizedBox(height: 24),

              VpsButton.primarySmall(
                title: VPTradingLocalize.current.trading_edit_order_button,
                onPressed: state.isValid ? _handleConfirm : null,
                width: double.infinity,
              ),
            ],
          );
        },
      ),
    );
  }
}
