import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/text_input_field.dart';

/// Example demonstrating the flexible layout options for TextInputField
class TextInputFieldLayoutExample extends StatefulWidget {
  const TextInputFieldLayoutExample({super.key});

  @override
  State<TextInputFieldLayoutExample> createState() => _TextInputFieldLayoutExampleState();
}

class _TextInputFieldLayoutExampleState extends State<TextInputFieldLayoutExample> {
  late TextEditingController _priceController1;
  late TextEditingController _volumeController1;
  late TextEditingController _priceController2;
  late TextEditingController _volumeController2;
  late PlaceOrderCubit _placeOrderCubit;
  late ValidateOrderCubit _validateOrderCubit;

  @override
  void initState() {
    super.initState();
    _priceController1 = TextEditingController();
    _volumeController1 = TextEditingController();
    _priceController2 = TextEditingController();
    _volumeController2 = TextEditingController();
    _placeOrderCubit = PlaceOrderCubit();
    _validateOrderCubit = ValidateOrderCubit();
  }

  @override
  void dispose() {
    _priceController1.dispose();
    _volumeController1.dispose();
    _priceController2.dispose();
    _volumeController2.dispose();
    _placeOrderCubit.close();
    _validateOrderCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TextInputField Layout Examples'),
      ),
      body: MultiBlocProvider(
        providers: [
          BlocProvider.value(value: _placeOrderCubit),
          BlocProvider.value(value: _validateOrderCubit),
        ],
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Example 1: Default Horizontal Layout
              const Text(
                '1. Default Horizontal Layout (Backward Compatible)',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'This is the existing behavior - price and volume fields side by side.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextInputField(
                priceController: _priceController1,
                volumeController: _volumeController1,
                // No layout parameter - defaults to horizontal
              ),
              
              const SizedBox(height: 32),
              
              // Example 2: Explicit Horizontal Layout with Custom Spacing
              const Text(
                '2. Horizontal Layout with Custom Spacing',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Horizontal layout with increased spacing between fields.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextInputField(
                priceController: _priceController1,
                volumeController: _volumeController1,
                layout: TextInputFieldLayout.horizontal,
                spacing: 20.0, // Custom spacing
              ),
              
              const SizedBox(height: 32),
              
              // Example 3: Vertical Layout
              const Text(
                '3. Vertical Layout',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Price and volume fields stacked vertically - useful for narrow screens or specific UI designs.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextInputField(
                priceController: _priceController2,
                volumeController: _volumeController2,
                layout: TextInputFieldLayout.vertical,
              ),
              
              const SizedBox(height: 32),
              
              // Example 4: Vertical Layout with Custom Spacing and Padding
              const Text(
                '4. Vertical Layout with Custom Spacing and Padding',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Vertical layout with custom spacing between fields and custom padding.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextInputField(
                priceController: _priceController2,
                volumeController: _volumeController2,
                layout: TextInputFieldLayout.vertical,
                spacing: 24.0, // Custom spacing between fields
                padding: const EdgeInsets.symmetric(horizontal: 32), // Custom padding
              ),
              
              const SizedBox(height: 32),
              
              // Example 5: Responsive Layout
              const Text(
                '5. Responsive Layout Example',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Layout changes based on screen width - horizontal for wide screens, vertical for narrow.',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              LayoutBuilder(
                builder: (context, constraints) {
                  final isWideScreen = constraints.maxWidth > 400;
                  return TextInputField(
                    priceController: _priceController2,
                    volumeController: _volumeController2,
                    layout: isWideScreen 
                        ? TextInputFieldLayout.horizontal 
                        : TextInputFieldLayout.vertical,
                    spacing: isWideScreen ? 12.0 : 16.0,
                  );
                },
              ),
              
              const SizedBox(height: 32),
              
              // Usage Notes
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Usage Notes:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Default layout is horizontal (backward compatible)\n'
                      '• Use TextInputFieldLayout.vertical for stacked fields\n'
                      '• Customize spacing between fields with spacing parameter\n'
                      '• Override default padding with padding parameter\n'
                      '• All existing validation and BLoC integration preserved\n'
                      '• Perfect for responsive designs and different UI layouts',
                      style: TextStyle(color: Colors.blue.shade700),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
