import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/edit_command_input_widget.dart';

void main() {
  group('EditCommandInputWidget', () {
    late TextEditingController controller;
    late ValidateOrderCubit validateOrderCubit;

    setUp(() {
      controller = TextEditingController();
      validateOrderCubit = ValidateOrderCubit();
    });

    tearDown(() {
      controller.dispose();
      validateOrderCubit.close();
    });

    Widget createWidget({
      required String label,
      VoidCallback? onDecrease,
      VoidCallback? onIncrease,
      String? errorText,
      bool isPrice = false,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider.value(
            value: validateOrderCubit,
            child: EditCommandInputWidget(
              label: label,
              controller: controller,
              onDecrease: onDecrease ?? () {},
              onIncrease: onIncrease ?? () {},
              errorText: errorText,
              isPrice: isPrice,
            ),
          ),
        ),
      );
    }

    testWidgets('should display label correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(label: 'Test Label'));

      expect(find.text('Test Label'), findsOneWidget);
    });

    testWidgets('should display error text when provided', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(
        label: 'Test Label',
        errorText: 'Test Error',
      ));

      expect(find.text('Test Error'), findsOneWidget);
    });

    testWidgets('should call onDecrease when decrease button is pressed', (WidgetTester tester) async {
      bool decreaseCalled = false;
      
      await tester.pumpWidget(createWidget(
        label: 'Test Label',
        onDecrease: () => decreaseCalled = true,
      ));

      await tester.tap(find.byIcon(Icons.remove));
      await tester.pump();

      expect(decreaseCalled, isTrue);
    });

    testWidgets('should call onIncrease when increase button is pressed', (WidgetTester tester) async {
      bool increaseCalled = false;
      
      await tester.pumpWidget(createWidget(
        label: 'Test Label',
        onIncrease: () => increaseCalled = true,
      ));

      await tester.tap(find.byIcon(Icons.add));
      await tester.pump();

      expect(increaseCalled, isTrue);
    });

    testWidgets('should update controller text when ValidateOrderCubit price changes', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(
        label: 'Price',
        isPrice: true,
      ));

      // Simulate price change in cubit
      validateOrderCubit.onChangePrice('25000');
      await tester.pump();

      expect(controller.text, '25000');
    });

    testWidgets('should update controller text when ValidateOrderCubit volume changes', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(
        label: 'Volume',
        isPrice: false,
      ));

      // Simulate volume change in cubit
      validateOrderCubit.onChangeVolumne('1000');
      await tester.pump();

      expect(controller.text, '1000');
    });

    testWidgets('should call ValidateOrderCubit.onChangePrice when text changes and isPrice is true', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(
        label: 'Price',
        isPrice: true,
      ));

      await tester.enterText(find.byType(VPTextField), '30000');
      await tester.pump();

      expect(validateOrderCubit.state.currentPrice, '30000');
    });

    testWidgets('should call ValidateOrderCubit.onChangeVolumne when text changes and isPrice is false', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget(
        label: 'Volume',
        isPrice: false,
      ));

      await tester.enterText(find.byType(VPTextField), '2000');
      await tester.pump();

      expect(validateOrderCubit.state.currentVolume, '2000');
    });
  });

  group('MaxVolumeInfoWidget', () {
    testWidgets('should display max volume correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaxVolumeInfoWidget(maxVolume: 10000),
          ),
        ),
      );

      expect(find.textContaining('10000'), findsOneWidget);
    });
  });
}
