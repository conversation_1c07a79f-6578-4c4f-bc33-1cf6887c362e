import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/text_input_field.dart';

void main() {
  group('TextInputField Layout Tests', () {
    late TextEditingController priceController;
    late TextEditingController volumeController;
    late PlaceOrderCubit placeOrderCubit;
    late ValidateOrderCubit validateOrderCubit;

    setUp(() {
      priceController = TextEditingController();
      volumeController = TextEditingController();
      placeOrderCubit = PlaceOrderCubit(symbol: 'VPB');
      validateOrderCubit = ValidateOrderCubit();
    });

    tearDown(() {
      priceController.dispose();
      volumeController.dispose();
      placeOrderCubit.close();
      validateOrderCubit.close();
    });

    Widget createWidget({
      TextInputFieldLayout layout = TextInputFieldLayout.horizontal,
      EdgeInsetsGeometry? padding,
      double? spacing,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: MultiBlocProvider(
            providers: [
              BlocProvider.value(value: placeOrderCubit),
              BlocProvider.value(value: validateOrderCubit),
            ],
            child: TextInputField(
              priceController: priceController,
              volumeController: volumeController,
              layout: layout,
              padding: padding,
              spacing: spacing,
            ),
          ),
        ),
      );
    }

    group('Horizontal Layout (Default)', () {
      testWidgets('should display fields in a row by default', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Should find a Row widget containing both input fields
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Column), findsNothing);
      });

      testWidgets(
        'should display fields in a row when explicitly set to horizontal',
        (WidgetTester tester) async {
          await tester.pumpWidget(
            createWidget(layout: TextInputFieldLayout.horizontal),
          );

          // Should find a Row widget containing both input fields
          expect(find.byType(Row), findsWidgets);
          expect(find.byType(Column), findsNothing);
        },
      );

      testWidgets('should use default spacing in horizontal layout', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Should find SizedBox with default width spacing
        final sizedBoxes = find.byType(SizedBox);
        expect(sizedBoxes, findsWidgets);
      });

      testWidgets('should use custom spacing in horizontal layout', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createWidget(spacing: 16.0));

        // Should find SizedBox with custom spacing
        final sizedBoxes = find.byType(SizedBox);
        expect(sizedBoxes, findsWidgets);
      });
    });

    group('Vertical Layout', () {
      testWidgets('should display fields in a column when set to vertical', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          createWidget(layout: TextInputFieldLayout.vertical),
        );

        // Should find a Column widget containing both input fields
        expect(find.byType(Column), findsWidgets);

        // Should find multiple ClipRRect widgets (one for each field)
        expect(find.byType(ClipRRect), findsWidgets);
      });

      testWidgets('should use default spacing in vertical layout', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          createWidget(layout: TextInputFieldLayout.vertical),
        );

        // Should find SizedBox with default height spacing
        final sizedBoxes = find.byType(SizedBox);
        expect(sizedBoxes, findsWidgets);
      });

      testWidgets('should use custom spacing in vertical layout', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          createWidget(layout: TextInputFieldLayout.vertical, spacing: 20.0),
        );

        // Should find SizedBox with custom spacing
        final sizedBoxes = find.byType(SizedBox);
        expect(sizedBoxes, findsWidgets);
      });
    });

    group('Custom Padding', () {
      testWidgets('should use default padding when not specified', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(createWidget());

        // Should find Padding widget with default EdgeInsets
        expect(find.byType(Padding), findsWidgets);
      });

      testWidgets('should use custom padding when specified', (
        WidgetTester tester,
      ) async {
        const customPadding = EdgeInsets.all(20.0);
        await tester.pumpWidget(createWidget(padding: customPadding));

        // Should find Padding widget with custom EdgeInsets
        expect(find.byType(Padding), findsWidgets);
      });
    });

    group('Backward Compatibility', () {
      testWidgets('should work with existing usage pattern', (
        WidgetTester tester,
      ) async {
        // Test that existing code without layout parameter still works
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MultiBlocProvider(
                providers: [
                  BlocProvider.value(value: placeOrderCubit),
                  BlocProvider.value(value: validateOrderCubit),
                ],
                child: TextInputField(
                  priceController: priceController,
                  volumeController: volumeController,
                  // No layout parameter - should default to horizontal
                ),
              ),
            ),
          ),
        );

        // Should default to horizontal layout (Row)
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Column), findsNothing);
      });
    });

    group('Layout Enum', () {
      test('should have correct enum values', () {
        expect(TextInputFieldLayout.values.length, 2);
        expect(
          TextInputFieldLayout.values,
          contains(TextInputFieldLayout.horizontal),
        );
        expect(
          TextInputFieldLayout.values,
          contains(TextInputFieldLayout.vertical),
        );
      });

      test('should have horizontal as default', () {
        final widget = TextInputField(
          priceController: TextEditingController(),
          volumeController: TextEditingController(),
        );
        expect(widget.layout, TextInputFieldLayout.horizontal);
      });
    });
  });
}
