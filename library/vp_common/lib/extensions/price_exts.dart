import 'package:intl/intl.dart';

import 'double_extensions.dart';

extension PriceExts on double {
  //trimZero If set to true, function will remove the .00 at the last of value
  String getPriceFormatted({
    bool convertToThousand = false,
    String currency = '',
    bool trimZero = false,
    bool prefix = false,
  }) {
    if (this == 0) {
      return '0$currency';
    }
    double value = this;
    int decimalDigits = 2;
    if (convertToThousand) {
      value = value / 1000;
    }

    if (trimZero) {
      if (value == value.round().toDouble()) {
        decimalDigits = 0;
      }
    }

    final NumberFormat format = NumberFormat.currency(
        locale: 'en-US', symbol: '', decimalDigits: decimalDigits);

    final prefixText = prefix && value > 0.0 ? '+' : '';

    return '$prefixText${format.format(value)}$currency';
  }

  String getPriceFormattedNumber(
      {bool convertToThousand = false,
      String currency = '',
      bool trimZero = false}) {
    if (this == 0) {
      return '0';
    }
    double value = this;
    int decimalDigits = 2;
    if (convertToThousand) {
      value = value / 1000;
    }

    if (trimZero) {
      if (value == value.round().toDouble()) {
        decimalDigits = 0;
      }
    }

    final NumberFormat format = NumberFormat.currency(
        locale: 'en-US', symbol: '', decimalDigits: decimalDigits);
    return format.format(value);
  }

  String getChangePercentDisplay({
    bool excludeZero = false,
    bool addCharacter = false,
  }) {
    if (toPrecision(2) == 0 && excludeZero) {
      return '0%';
    }

    if (toPrecision(2) == 100) {
      return addCharacter ? '+100%' : '100%';
    }

    final value = '${toStringAsFixed(2)}%';

    if (!value.trim().startsWith('+') && this > 0 && addCharacter) {
      return '+$value';
    }

    return value;
  }

  String toPercent({
    int fractionDigits = 2,
    bool addPrefixCharacter = true,
    bool truncateZero = false,
  }) {
    if (this == 0) return '0%';

    if (this == 100 && addPrefixCharacter) return '+100%';

    var value = abs().toStringAsFixed(fractionDigits);

    if (truncateZero) {
      if (toInt() == this) {
        value = '${abs().toInt()}';
      } else if (double.parse(value) % 1 == 0) {
        value = '${double.parse(value).abs().toInt()}';
      } else {
        value = double.parse(value).truncateZero().toString();
      }
    }

    if (this > 0 && addPrefixCharacter) {
      return '+$value%';
    }

    if (this < 0 && addPrefixCharacter) {
      return '-$value%';
    }

    return '$value%';
  }

  String get marketIndexChange {
    if (toPrecision(2) == 0) {
      return '0';
    }
    return abs().toStringAsFixed(2);
  }

  String getMarketChangePercentDisplay({int fractionDigits = 2}) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }

    if (this > 0) {
      return '${toStringAsFixed(fractionDigits)}%';
    }

    return '${toStringAsFixed(fractionDigits)}%';
  }

  String getValuePercentAbs({
    int fractionDigits = 2,
    bool isAbs = true,
    bool removeFractionIfInteger = false,
  }) {
    if (this == 0) {
      return '0%';
    }

    if (this == 100) {
      return '100%';
    }

    if (removeFractionIfInteger && this % 1 == 0) {
      return '${toInt()}%';
    }

    double value = isAbs ? abs() : this;

    return '${value.toStringAsFixed(fractionDigits)}%';
  }

  String getVolumeDisplay() {
    NumberFormat currencyFormat = NumberFormat("#,###.##", "en_ES");
    double volume = this;
    if (volume >= 100000) {
      volume = volume / 1000000;
      return '${currencyFormat.format(volume)}M';
    }
    if (volume > 100) {
      volume = volume / 1000;
      return '${currencyFormat.format(volume)}k';
    }
    return volume.toInt().toString();
  }

  String getIndexFormatted() {
    NumberFormat currencyFormat = NumberFormat("#,###.##", "en_ES");
    var indexFormatted = currencyFormat.format(this);
    if (indexFormatted.contains(RegExp(r'\.\d$'))) {
      indexFormatted += '0';
    }
    return indexFormatted;
  }

  String thousandFormat() {
    NumberFormat currencyFormat = NumberFormat("#,###,###", "en_ES");
    return currencyFormat.format(this);
  }

  String getMillionUpperUnit(
      {required String million, required String billion}) {
    String formatValue = '';
    if (this < 1000000000) {
      formatValue = '${(this / 1000000).toPrecision(1)} $million';
    } else {
      formatValue = '${(this / 1000000000).toPrecision(1)} $billion';
    }
    return formatValue;
  }
}

extension PriceExtsString on String {
  String getPriceFormatted() {
    if (this == 'ATC' || this == 'ATO') {
      return this;
    }
    try {
      double price = (double.tryParse(this) ?? 0) / 1000;
      NumberFormat currencyFormat =
          NumberFormat.currency(locale: 'en_ES', decimalDigits: 2, symbol: '');
      return currencyFormat.format(price);
    } catch (e) {
      return '0';
    }
  }
}
