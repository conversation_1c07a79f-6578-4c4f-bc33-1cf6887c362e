 



Tên	<PERSON><PERSON> tả
Toggle button "Sửa giá"
& "Sửa khối lượng"	Cho phép KH chuyển giữa Sửa giá và khối lượng.
Hệ thống enable thông tin tương ứng được chọn và disable thông tin còn lại.
Gi<PERSON> đặt lệnh	•	Cho phép Khách hàng nhập giá sửa.
	•	Đơn vị giá: 1000 đ ( gi<PERSON> nhập vào hoặc giá lấy lên khi KH click Label giá là giá đã /1000)
	•	Khi Khách hàng nhập giá, kiểm tra giá trị khách hàng nhập vào ô giá:
	•	Format: theo rule 1. Format
	•	Bước giá: theo rule 2. Bước giá

1. Format
	•	Default hiển thị giá đặt của lệnh (Lấy từ API Sổ lệnh)
	•	Không được để trống: nếu đã nhập giá, sau đó xóa=> hệ thống hiển thông báo "Vui lòng nhập giá"
	•	Chỉ cho phép user nhập giá trị số và kí tự dấu chấm
	•	Client ghi nhận action nhập từ KH. Trường hợp nhập không phải số và dấu chấm thì không ghi nhận kí tự nhập.
	•	Trường hợp ghi nhận ký tự đầu tiên là số 0 thì tự động remove giá trị. Chỉ ghi nhận tối đa là 2 chữ số sau phần thập phân (VD: 10.05, 8.88). Cho phép nhập tối đa là 11 ký tự (bao gồm cả dấu .). Quá 11 kí tự không ghi nhận thêm.
	•	Giá nhập vào phải thỏa mãn điều kiện: giá sàn <= giá nhập <= giá trần của mã chứng khoán đang lựa chọn. Giá trần, giá sàn được xác định qua API lấy giá CP . Nếu không thỏa mãn hiển thị thông báo "Nhập giá trong khoảng trần sàn"
	•	Tự động thêm dấu phấy "," để ngăn cách dữ liệu giữa hàng nghìn, hàng triệu, hàng tỉ (VD: 3,111,234,431)
2. Bước giá
Sàn
Đơn vị yết giá
Note
HOSE
	•	 Cổ phiếu, chứng chỉ quỹ đóng
Mức giá
Đơn vị yết giá
<10.000 VND
10 VND
>=10.000 và <50.000 VND
50 VND
>= 50.000 VND
100 VND
	•	Chứng chỉ quỹ ETF, chứng quyền: áp dụng đơn vị yết giá 10 đồng cho tất cả các mức giá
	•	 Đơn vị yết giá đối với giao dịch thỏa thuận là 1 đồng
Dựa trên thông tin sàn của mã chứng khoán: giá trị biến "exchange" của api: FSS: /quotes để xác định rule của mã: 
	•	Nếu stockType của mã chứng khoán = 2/12 thì:  
	•	Nếu mã thuộc HNX/UPCOM: Giá nhập vào phải thỏa mãn chia hết cho 100đ (VD: 10.1, 0.1, 90.9)
	•	Nếu mã thuộc HOSE: Chia làm 3 trường hợp:
	•	Nếu giá nhập vào < 10.000đ: giá phải chia hết cho 10đ (VD: 8.2, 7,91, 9.99)
	•	Nếu giá nhập vào trong khoảng 10.000 - 50.000 đ: giá phải chia hết cho 50đ (VD:  10.0, 20,15, 49.95)
	•	Nếu giá nhập vào > 50000đ: Giá phải chia hết cho 100 đ (VD: 51.0, 62.9, 100.3)
	•	Nhập sai giá trị về giá → hiển thị thông báo: "Sai bước giá" - màu đỏ.
	•	Nếu stockType của mã chứng khoán = 3/4 (ETF/CCQ/CW) thì:
	•	Giá nhập vào phải chia hết cho 10đ (VD: 1.11, 19.97, 29.36,...)
HNX
	•	Giao dịch khớp lệnh cổ phiếu, trái phiếu: 100 đồng
	•	Giao dịch thỏa thuận cổ phiếu, trái phiếu: 1 đồng
	•	Giao dịch chứng chỉ quỹ ETF: 1 đồng
UPCOM
	•	Đơn vị yết giá đối với cổ phiếu: 100 đồng
	•	Không quy định đơn vị yết giá đối với giao dịch thỏa thuận và chứng khoán khác
Nếu không thỏa mãn điều kiện Bước giá, hiển thị thông báo "Bước giá không hợp lệ"
Button tăng/giảm giá	•	User click vào, giá sẽ thực hiện tăng/giảm giá trị giá đang nhập trong ô giá 1 bước giá (bước giá check theo điều kiện sàn, theo mức giá)
	•	– VD: sàn HOSE, giá đang nhập: 10.0, click giảm → 9.99, 20.3 → 20.25. Sàn HNX, giá đang nhập: 21 click tăng → 21.1, 10 → 10.1)
Khối lượng	•	Cho phép Khách hàng nhập Khối lượng Sửa.
	•	Với lệnh gốc là Lô lẻ: 1<= KL<100, KL  và KL nhập vào phải là bội số của 1.
	•	Với lệnh gốc là Lô chẵn: 100<= KL<= KL tối đa theo Sàn, và KL nhập vào phải là bội số của100.
	•	Khi Khách hàng nhập Khối lượng, kiểm tra giá trị khách hàng nhập vào:
	•	Format: theo rule 1.Format
	•	Validate giá trị Khối lượng tối đa: rule 2. Khối lượng tối đa
	•	Validate giá trị khối lượng đặt theo loại lệnh: rule 3. Khối lượng đặt theo loại lệnh

1. Format:
	•	Default hiển thị KL đặt của lệnh (Lấy từ API Sổ lệnh)
	•	Khối lượng: chữ số màu trắng
	•	Chỉ cho phép nhập giá trị số nguyên dương.
	•	KH nhập giá trị số 0 đầu tiên thì tự động remove, tối đa 10 kí tự, quá 10 kí tự không cho phép nhập thêm.
	•	Tự động thêm dấu phấy "," để ngăn cách dữ liệu giữa hàng nghìn, hàng triệu, hàng tỉ (VD: 3,111,234,431)
2. Khối lượng tối đa:
KL nhập vào phải thỏa mãn điều kiện:
	•	Lệnh Mua: Khối lượng nhập <= KL mua tối đa
	•	Lệnh Bán: Khối lượng nhập <= KL bán tối đa
	•	Validate giá trị Khối lượng tối đa (Lấy từ API Available Trade)
3. Các rule validate lệnh khác do FLEX/FDS quy định, khi đẩy vào core sẽ tự validate
Button tăng/giảm Khối lượng	
Button "Sửa lệnh"	•	Disable: Nếu Khối lượng hoặc Giá được nhập không thay đổi so với Lệnh gốc
	•	Enable: Nếu Khối lượng hoặc Giá được nhập có thay đổi so với Lệnh gốc

Luồng xác nhận Sửa lệnh + xác thực sẽ theo rule sau:
	•	Lưu xác thực trong Phiên = ON:  Thực hiện sửa lệnh mà không cần xác thực
	•	Lưu xác thực trong Phiên = OFF:  Hiển thị màn nhập PIN (hoặc màn xác thực MFA tương ứng)→ Thực hiện sửa lệnh
Button "Hủy"	Đóng modal
Sàn	Đơn vị yết giá	Note
HOSE	•	 Cổ phiếu, chứng chỉ quỹ đóng
Mức giá
Đơn vị yết giá
<10.000 VND
10 VND
>=10.000 và <50.000 VND
50 VND
>= 50.000 VND
100 VND
	•	Chứng chỉ quỹ ETF, chứng quyền: áp dụng đơn vị yết giá 10 đồng cho tất cả các mức giá
	•	 Đơn vị yết giá đối với giao dịch thỏa thuận là 1 đồng	Dựa trên thông tin sàn của mã chứng khoán: giá trị biến "exchange" của api: FSS: /quotes để xác định rule của mã: 
	•	Nếu stockType của mã chứng khoán = 2/12 thì:  
	•	Nếu mã thuộc HNX/UPCOM: Giá nhập vào phải thỏa mãn chia hết cho 100đ (VD: 10.1, 0.1, 90.9)
	•	Nếu mã thuộc HOSE: Chia làm 3 trường hợp:
	•	Nếu giá nhập vào < 10.000đ: giá phải chia hết cho 10đ (VD: 8.2, 7,91, 9.99)
	•	Nếu giá nhập vào trong khoảng 10.000 - 50.000 đ: giá phải chia hết cho 50đ (VD:  10.0, 20,15, 49.95)
	•	Nếu giá nhập vào > 50000đ: Giá phải chia hết cho 100 đ (VD: 51.0, 62.9, 100.3)
	•	Nhập sai giá trị về giá → hiển thị thông báo: "Sai bước giá" - màu đỏ.
	•	Nếu stockType của mã chứng khoán = 3/4 (ETF/CCQ/CW) thì:
	•	Giá nhập vào phải chia hết cho 10đ (VD: 1.11, 19.97, 29.36,...)
HNX	•	Giao dịch khớp lệnh cổ phiếu, trái phiếu: 100 đồng
	•	Giao dịch thỏa thuận cổ phiếu, trái phiếu: 1 đồng
	•	Giao dịch chứng chỉ quỹ ETF: 1 đồng	
UPCOM	•	Đơn vị yết giá đối với cổ phiếu: 100 đồng
	•	Không quy định đơn vị yết giá đối với giao dịch thỏa thuận và chứng khoán khác	
Mức giá	Đơn vị yết giá
<10.000 VND	10 VND
>=10.000 và <50.000 VND	50 VND
>= 50.000 VND	100 VND
	
Khối lượng 1 → 99	Tăng/Giảm 1 đơn vị

Lệnh Sửa Mua	Map giá trị "maxBuyQty"
Lệnh Sửa Bán	Map giá trị "maxSellQty"
			
API Sửa lệnh	2. trading/orders (Sửa lệnh)
PUT: trading/orders	Gửi yêu cầu Sửa lệnh tới Core	Input
accountId = Số tiểu khoản từ API Lấy TK
orderId = Số hiệu lệnh Sửa (lấy từ Sổ lệnh)
qty = Khối lượng sửa
limitPrice = Giá sửa
API preCheckOrder	Không sử dụng	N/A	N/A
API sức mua	.accounts/availableTrade
GET: accounts/availableTrade	Check sức mua với lệnh Mua	Input
accountId = Số tiểu khoản từ API Lấy TK
symbol = Mã chứng khoán
quotePrice = Giá sửa lệnh
API danh mục chứng khoán	2. portfolio/stocks	Check sức bán với lệnh Bán	
